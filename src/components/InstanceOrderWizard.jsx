import React, { useState, useMemo } from 'react';
import {
  Modal,
  Steps,
  Card,
  Button,
  Form,
  Input,
  Select,
  Switch,
  Divider,
  Alert,
  Statistic,
  Segmented,
  Slider,
} from 'antd';
import {
  Server,
  Package,
  Settings,
  DollarSign,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
} from 'lucide-react';
import { formatPrice } from '@/utils/format';

const { Option } = Select;
const { TextArea } = Input;

const InstanceOrderWizard = ({
  visible,
  onCancel,
  onConfirm,
  device,
  application = null,
  title = '订购实例',
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 订购配置状态
  const [orderConfig, setOrderConfig] = useState({
    instanceName: '',
    pricingModel: 'ON_DEMAND', // 计费模式：只支持按需计费
    duration: 1, // 时长（小时）
    gpuCount: 1, // GPU 卡数
    extraDiskSize: 0, // 额外磁盘大小（GB，步进10G）
    autoInstallApp: true,
    autoRenew: false,
    appConfig: {},
  });

  // 动态生成步骤
  const hasAppConfig = application && orderConfig.autoInstallApp;
  const steps = [
    {
      title: '确认配置',
      description: '确认设备和应用信息',
      icon: <Server className="w-4 h-4" />,
    },
    ...(hasAppConfig
      ? [
          {
            title: '应用配置',
            description: '配置应用参数',
            icon: <Settings className="w-4 h-4" />,
          },
        ]
      : []),
    {
      title: '订单确认',
      description: '确认订单并支付',
      icon: <CheckCircle className="w-4 h-4" />,
    },
  ];

  // 使用 useMemo 确保价格在 orderConfig 变化时重新计算
  const priceInfo = useMemo(() => {
    console.log('Recalculating price info due to dependency change');
    console.log('Current orderConfig:', orderConfig);
    console.log('Full device object:', device);
    console.log('Device resourcePricingList:', device?.resourcePricingList);
    console.log('Device pricePerHour:', device?.pricePerHour);

    // 检查是否有详细的价格列表（来自设备管理页面）
    if (device?.resourcePricingList && device.resourcePricingList.length > 0) {
      console.log('Using detailed pricing data from resourcePricingList');

      // 获取 GPU 价格（按小时计费）
      const gpuPricing = device.resourcePricingList.find(
        p => p.resourceType === 'GPU'
      );
      console.log('GPU pricing found:', gpuPricing);
      const gpuUnitPrice = gpuPricing?.pricePerUnit || 0;
      const gpuHourlyPrice = gpuUnitPrice * orderConfig.gpuCount;

      // 获取磁盘价格（按小时计费）
      const diskPricing = device.resourcePricingList.find(
        p => p.resourceType === 'DISK'
      );
      console.log('Disk pricing found:', diskPricing);
      const diskUnitPrice = diskPricing?.pricePerUnit || 0;
      const diskHourlyPrice = (orderConfig.extraDiskSize / 10) * diskUnitPrice; // 按10G计费

      // 应用价格
      const appPrice = application?.pricePerHour || 0;
      const appCost = orderConfig.autoInstallApp ? appPrice : 0;

      // 总价计算（统一按小时计费）
      const hourlyTotal = gpuHourlyPrice + diskHourlyPrice + appCost;
      const totalCost = hourlyTotal * orderConfig.duration;

      const result = {
        gpuPrice: gpuHourlyPrice,
        diskPrice: diskHourlyPrice,
        appPrice,
        appCost,
        totalCost,
        gpuUnitPrice,
        diskUnitPrice,
        hourlyTotal,
      };

      console.log('Price calculation result (detailed):', result);
      return result;
    }

    // 使用简单的价格数据（来自市场页面）
    if (device?.pricePerHour) {
      console.log('Using simple pricing data from pricePerHour');

      // 假设 pricePerHour 是整机价格，按GPU数量分摊
      const gpuUnitPrice = device.pricePerHour / (device.gpuNum || 1);
      const gpuHourlyPrice = gpuUnitPrice * orderConfig.gpuCount;

      // 磁盘价格估算（假设每10GB 0.01元/小时）
      const diskUnitPrice = 0.01;
      const diskHourlyPrice = (orderConfig.extraDiskSize / 10) * diskUnitPrice;

      // 应用价格
      const appPrice = application?.pricePerHour || 0;
      const appCost = orderConfig.autoInstallApp ? appPrice : 0;

      // 总价计算
      const hourlyTotal = gpuHourlyPrice + diskHourlyPrice + appCost;
      const totalCost = hourlyTotal * orderConfig.duration;

      const result = {
        gpuPrice: gpuHourlyPrice,
        diskPrice: diskHourlyPrice,
        appPrice,
        appCost,
        totalCost,
        gpuUnitPrice,
        diskUnitPrice,
        hourlyTotal,
      };

      console.log('Price calculation result (simple):', result);
      return result;
    }

    // 没有价格数据
    console.log('No pricing data available');
    return {
      gpuPrice: 0,
      diskPrice: 0,
      appCost: 0,
      appPrice: 0,
      totalCost: 0,
      gpuUnitPrice: 0,
      diskUnitPrice: 0,
      hourlyTotal: 0,
    };
  }, [device, orderConfig, application]);

  const handleNext = async () => {
    if (currentStep === 0) {
      try {
        // 根据是否有应用来决定验证哪些字段
        const fieldsToValidate = ['instanceName', 'duration', 'autoRenew'];
        if (application) {
          fieldsToValidate.push('autoInstallApp');
        }
        const values = await form.validateFields(fieldsToValidate);
        setOrderConfig(prev => ({ ...prev, ...values }));

        // 如果有应用且选择自动安装，进入应用配置步骤（步骤1）
        // 否则直接跳到最终确认步骤（在两步流程中也是步骤1）
        setCurrentStep(1);
      } catch (error) {
        console.error('表单验证失败:', error);
      }
    } else if (currentStep === 1) {
      try {
        const appConfigValues = await form.validateFields();
        setOrderConfig(prev => ({
          ...prev,
          appConfig: appConfigValues,
        }));
        setCurrentStep(2);
      } catch (error) {
        console.error('应用配置验证失败:', error);
      }
    }
  };

  const handlePrev = () => {
    // 总是回到上一步
    setCurrentStep(currentStep - 1);
  };

  const handleConfirm = async () => {
    setLoading(true);
    try {
      const finalConfig = {
        ...orderConfig,
        device,
        application: orderConfig.autoInstallApp ? application : null,
        totalCost: priceInfo.totalCost,
        pricePerHour:
          priceInfo.gpuPrice + priceInfo.diskPrice + priceInfo.appCost,
        priceInfo,
      };

      await onConfirm(finalConfig);
    } catch (error) {
      console.error('订购失败:', error);
      setLoading(false);
    }
    // 注意：不在这里设置loading为false，因为需要保持loading状态直到跳转完成
  };

  const handleCancel = () => {
    form.resetFields();
    setCurrentStep(0);
    setOrderConfig({
      instanceName: '',
      pricingModel: 'ON_DEMAND',
      duration: 1,
      gpuCount: 1,
      extraDiskSize: 0,
      autoInstallApp: true,
      autoRenew: false,
      appConfig: {},
    });
    onCancel();
  };

  // 步骤1：确认配置
  const renderStep1 = () => (
    <div className="space-y-6">
      {/* 设备信息 */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <Server className="w-4 h-4" />
            <span>设备信息</span>
          </div>
        }
        size="small"
      >
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-gray-600">设备名称</div>
            <div className="font-medium">
              {device?.machineName || device?.name}
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">设备类型</div>
            <div className="font-medium">
              {device?.machineType || device?.type}
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">CPU</div>
            <div className="font-medium">
              {device?.cpuName || device?.cpu} ({device?.cpuNum}核)
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">内存</div>
            <div className="font-medium">
              {device?.memorySize || device?.memory}GB
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">GPU</div>
            <div className="font-medium">
              {device?.gpuName || device?.gpu} (最多{device?.gpuNum}卡)
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">存储</div>
            <div className="font-medium">
              {device?.diskSize || device?.storage}GB {device?.diskType}
            </div>
          </div>
        </div>
      </Card>

      {/* 应用信息 */}
      {application && (
        <Card
          title={
            <div className="flex items-center space-x-2">
              <Package className="w-4 h-4" />
              <span>应用信息</span>
            </div>
          }
          size="small"
        >
          <div className="flex items-center space-x-4 mb-3">
            <div className="text-2xl">{application.icon}</div>
            <div>
              <div className="font-medium">{application.name}</div>
              <div className="text-sm text-gray-600">{application.version}</div>
            </div>
          </div>
          <div className="text-sm text-gray-700 mb-3">
            {application.description}
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">应用价格</span>
              <span className="font-medium text-green-600">
                {priceInfo.appPrice > 0
                  ? `${formatPrice(priceInfo.appPrice)}/小时`
                  : '免费'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">
                {priceInfo.appPrice > 0 ? '自动购买并安装' : '自动安装'}
              </span>
              <Switch
                checked={orderConfig.autoInstallApp}
                onChange={checked =>
                  setOrderConfig(prev => ({
                    ...prev,
                    autoInstallApp: checked,
                  }))
                }
              />
            </div>
          </div>
        </Card>
      )}

      {/* 实例配置 */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>实例配置</span>
          </div>
        }
        size="small"
      >
        <Form
          form={form}
          layout="horizontal"
          labelAlign="left"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <Form.Item
            name="instanceName"
            label="实例名称"
            rules={[{ required: true, message: '请输入实例名称' }]}
            initialValue={application ? `${application.name} 实例` : '我的实例'}
          >
            <Input
              placeholder="请输入实例名称"
              onChange={e =>
                setOrderConfig(prev => ({
                  ...prev,
                  instanceName: e.target.value,
                }))
              }
            />
          </Form.Item>

          <div className="grid grid-cols-1 gap-4">
            <Form.Item label="计费模式" className="mb-0">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  <span className="text-sm font-medium text-blue-700">
                    按需计费（小时）
                  </span>
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  按实际使用时长计费，灵活便捷
                </div>
              </div>
            </Form.Item>

            <Form.Item
              name="duration"
              label="使用时长"
              rules={[{ required: true, message: '请输入使用时长' }]}
              initialValue={1}
              className="mb-0"
            >
              <div className="flex items-center space-x-4">
                <Slider
                  min={1}
                  max={168}
                  value={orderConfig.duration}
                  onChange={value =>
                    setOrderConfig(prev => ({
                      ...prev,
                      duration: value,
                    }))
                  }
                  className="flex-1"
                  marks={{
                    1: '1小时',
                    24: '1天',
                    168: '1周',
                  }}
                />
                <span className="text-sm text-gray-600 min-w-[80px]">
                  {orderConfig.duration} 小时
                </span>
              </div>
            </Form.Item>

            <Form.Item
              name="autoRenew"
              label="自动续费"
              initialValue={false}
              className="mb-0"
            >
              <Switch
                checked={orderConfig.autoRenew}
                onChange={checked =>
                  setOrderConfig(prev => ({
                    ...prev,
                    autoRenew: checked,
                  }))
                }
              />
            </Form.Item>
            <Form.Item label="GPU卡数" className="mb-0">
              <Select
                value={orderConfig.gpuCount}
                onChange={value =>
                  setOrderConfig(prev => ({ ...prev, gpuCount: value }))
                }
                className="w-full"
              >
                {Array.from({ length: device?.gpuNum || 1 }, (_, i) => (
                  <Option key={i + 1} value={i + 1}>
                    {i + 1}卡
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="额外磁盘" className="mb-0">
              <div className="flex items-center space-x-4">
                <Slider
                  min={0}
                  max={1000}
                  step={10}
                  value={orderConfig.extraDiskSize}
                  onChange={value =>
                    setOrderConfig(prev => ({
                      ...prev,
                      extraDiskSize: value,
                    }))
                  }
                  className="flex-1"
                  marks={{
                    0: '0GB',
                    1000: '1TB',
                  }}
                />
                <span className="text-sm text-gray-600 min-w-[80px]">
                  {orderConfig.extraDiskSize >= 1000
                    ? `${(orderConfig.extraDiskSize / 1000).toFixed(1)}TB`
                    : `${orderConfig.extraDiskSize}GB`}
                </span>
              </div>
              {orderConfig.extraDiskSize > 0 && (
                <div className="text-xs text-gray-500 mt-1">
                  额外费用: {formatPrice(priceInfo.diskPrice)}/小时
                </div>
              )}
            </Form.Item>
          </div>
        </Form>
      </Card>

      {/* 价格预览 */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <DollarSign className="w-4 h-4" />
            <span>价格预览</span>
          </div>
        }
        size="small"
      >
        <div className="space-y-3">
          <div className="flex justify-between">
            <span>GPU费用 ({orderConfig.gpuCount}卡)</span>
            <span>{formatPrice(priceInfo.gpuPrice)}/小时</span>
          </div>
          {orderConfig.extraDiskSize > 0 && (
            <div className="flex justify-between">
              <span>额外磁盘费用 ({orderConfig.extraDiskSize}GB)</span>
              <span>{formatPrice(priceInfo.diskPrice)}/小时</span>
            </div>
          )}
          {orderConfig.autoInstallApp && priceInfo.appCost > 0 && (
            <div className="flex justify-between">
              <span>应用费用</span>
              <span>{formatPrice(priceInfo.appCost)}/小时</span>
            </div>
          )}
          <Divider />
          <div className="flex justify-between">
            <span>小时单价合计</span>
            <span>{formatPrice(priceInfo.hourlyTotal)}/小时</span>
          </div>
          <div className="flex justify-between">
            <span>使用时长</span>
            <span>{orderConfig.duration} 小时</span>
          </div>
          <Divider />
          <div className="flex justify-between font-medium text-lg">
            <span>总计费用</span>
            <span className="text-blue-600">
              {formatPrice(priceInfo.totalCost)}
            </span>
          </div>
        </div>
      </Card>
    </div>
  );

  // 步骤2：应用配置
  const renderStep2 = () => (
    <div className="space-y-6">
      <Alert
        message="应用配置"
        description={`为 ${application?.name} 配置运行参数`}
        type="info"
        showIcon
      />

      <Card title="应用参数配置" size="small">
        <Form form={form} layout="vertical">
          <Form.Item
            name="appPort"
            label="应用端口"
            rules={[{ required: true, message: '请输入应用端口' }]}
            initialValue={application?.ports?.[0] || 8080}
          >
            <Input type="number" placeholder="8080" />
          </Form.Item>

          <Form.Item
            name="appEnv"
            label="环境变量"
            help="每行一个，格式：KEY=VALUE"
          >
            <TextArea
              rows={4}
              placeholder="NODE_ENV=production&#10;API_KEY=your_api_key"
            />
          </Form.Item>

          <Form.Item
            name="appCommand"
            label="启动命令（可选）"
            help="如果为空，将使用默认启动命令"
          >
            <Input placeholder="npm start" />
          </Form.Item>

          <Form.Item
            name="appVolumes"
            label="数据卷挂载（可选）"
            help="格式：宿主机路径:容器路径"
          >
            <Input placeholder="/data:/app/data" />
          </Form.Item>
        </Form>
      </Card>
    </div>
  );

  // 步骤3：订单确认
  const renderStep3 = () => (
    <div className="space-y-6">
      <Alert
        message="订单确认"
        description="请确认以下订单信息，确认后将开始创建实例"
        type="warning"
        showIcon
      />

      {/* 订单摘要 */}
      <Card title="订单摘要" size="small">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span>实例名称</span>
            <span className="font-medium">{orderConfig.instanceName}</span>
          </div>
          <div className="flex justify-between">
            <span>设备</span>
            <span className="font-medium">{device?.name}</span>
          </div>
          <div className="flex justify-between">
            <span>计费模式</span>
            <span className="font-medium">按需计费（小时）</span>
          </div>
          <div className="flex justify-between">
            <span>使用时长</span>
            <span className="font-medium">{orderConfig.duration} 小时</span>
          </div>
          <div className="flex justify-between">
            <span>GPU卡数</span>
            <span className="font-medium">{orderConfig.gpuCount}卡</span>
          </div>
          {orderConfig.extraDiskSize > 0 && (
            <div className="flex justify-between">
              <span>额外磁盘</span>
              <span className="font-medium">{orderConfig.extraDiskSize}GB</span>
            </div>
          )}
          {orderConfig.autoInstallApp && application && (
            <div className="flex justify-between">
              <span>应用</span>
              <span className="font-medium">{application.name}</span>
            </div>
          )}
          <Divider className="my-2" />
          <div className="flex justify-between text-lg font-bold">
            <span>总费用</span>
            <span className="text-red-600">
              {formatPrice(priceInfo.totalCost)}
            </span>
          </div>
        </div>
      </Card>

      {/* 费用明细 */}
      <Card title="费用明细" size="small">
        <div className="space-y-3">
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-gray-700 mb-2">
              小时费用明细
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>GPU费用 ({orderConfig.gpuCount}卡)</span>
                <span>
                  {formatPrice(priceInfo.gpuUnitPrice)} × {orderConfig.gpuCount}
                  卡 = {formatPrice(priceInfo.gpuPrice)}/小时
                </span>
              </div>
              {orderConfig.extraDiskSize > 0 && (
                <div className="flex justify-between">
                  <span>额外磁盘费用 ({orderConfig.extraDiskSize}GB)</span>
                  <span>
                    {formatPrice(priceInfo.diskUnitPrice)} ×{' '}
                    {orderConfig.extraDiskSize / 10}个10GB ={' '}
                    {formatPrice(priceInfo.diskPrice)}/小时
                  </span>
                </div>
              )}
              {orderConfig.autoInstallApp &&
                application &&
                priceInfo.appCost > 0 && (
                  <div className="flex justify-between">
                    <span>应用费用</span>
                    <span>{formatPrice(priceInfo.appCost)}/小时</span>
                  </div>
                )}
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>小时单价合计</span>
                <span>{formatPrice(priceInfo.hourlyTotal)}/小时</span>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-blue-700 mb-2">
              总费用计算
            </div>
            <div className="text-sm text-blue-600">
              {formatPrice(priceInfo.hourlyTotal)}/小时 × {orderConfig.duration}
              小时 = {formatPrice(priceInfo.totalCost)}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      maskClosable={false}
      destroyOnClose={true}
    >
      <div className="space-y-6">
        {/* 步骤指示器 */}
        <Steps current={currentStep} size="small">
          {steps.map((step, index) => (
            <Steps.Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
            />
          ))}
        </Steps>

        {/* 步骤内容 */}
        <div className="min-h-[400px]">
          {currentStep === 0 && renderStep1()}
          {currentStep === 1 && hasAppConfig && renderStep2()}
          {currentStep === 1 && !hasAppConfig && renderStep3()}
          {currentStep === 2 && renderStep3()}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-between">
          <div>
            {currentStep > 0 && (
              <Button
                onClick={handlePrev}
                icon={<ArrowLeft className="w-4 h-4" />}
              >
                上一步
              </Button>
            )}
          </div>
          <div className="space-x-2">
            <Button onClick={handleCancel}>取消</Button>
            {/* 判断是否是最后一步 */}
            {currentStep === 2 || (currentStep === 1 && !hasAppConfig) ? (
              <Button type="primary" loading={loading} onClick={handleConfirm}>
                {loading ? '实例创建中...' : '确认订购'}
              </Button>
            ) : (
              <Button
                type="primary"
                onClick={handleNext}
                icon={<ArrowRight className="w-4 h-4" />}
              >
                下一步
              </Button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default InstanceOrderWizard;
