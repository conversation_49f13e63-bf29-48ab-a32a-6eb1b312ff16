import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Timeline,
  Typography
} from 'antd';
import ReactECharts from 'echarts-for-react';
import {
  Server,
  Cpu,
  DollarSign,
  TrendingUp,
  ShoppingCart,
  HardDrive,
  Store,
  User,
  ArrowUpRight
} from 'lucide-react';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const navigate = useNavigate();

  // 模拟数据
  const stats = [
    {
      title: '运行实例',
      value: 3,
      suffix: '个',
      description: '当前活跃实例数量',
      icon: <Server className="w-6 h-6" />,
      color: '#1890ff',
      trend: 12.5,
    },
    {
      title: '总算力',
      value: 48,
      suffix: '核',
      description: '已分配的计算核心',
      icon: <Cpu className="w-6 h-6" />,
      color: '#52c41a',
      trend: 8.2,
    },
    {
      title: '本月支出',
      value: 1248,
      prefix: '¥',
      description: '当月算力费用',
      icon: <DollarSign className="w-6 h-6" />,
      color: '#fa8c16',
      trend: -5.3,
    },
    {
      title: '资源利用率',
      value: 86,
      suffix: '%',
      description: '平均资源使用率',
      icon: <TrendingUp className="w-6 h-6" />,
      color: '#722ed1',
      trend: 15.8,
    },
  ];

  // 费用趋势数据
  const costTrendData = [890, 1120, 980, 1350, 1180, 1248];
  const months = ['1月', '2月', '3月', '4月', '5月', '6月'];

  // 实例状态数据
  const instanceStatusData = [
    { name: '运行中', value: 3, itemStyle: { color: '#52c41a' } },
    { name: '已停止', value: 2, itemStyle: { color: '#faad14' } },
    { name: '维护中', value: 1, itemStyle: { color: '#ff4d4f' } },
  ];

  // 费用趋势图配置
  const costTrendOption = {
    title: {
      text: '',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        return `${params[0].name}<br/>费用: ¥${params[0].value}`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8',
        },
      },
    },
    series: [
      {
        name: '费用',
        type: 'line',
        smooth: true,
        data: costTrendData,
        lineStyle: {
          color: '#1890ff',
          width: 3,
        },
        itemStyle: {
          color: '#1890ff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.05)',
              },
            ],
          },
        },
      },
    ],
  };

  // 实例状态饼图配置
  const instanceStatusOption = {
    title: {
      text: '',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      left: 'center',
    },
    series: [
      {
        name: '实例状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: instanceStatusData,
      },
    ],
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* 页面标题 */}
      <div className="mb-6">
        <Title level={2} className="!mb-2">控制面板</Title>
        <Text type="secondary">欢迎使用帆一异构智算云平台</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card
              hoverable
              className="h-full"
              styles={{ body: { padding: '20px' } }}
            >
              <div className="flex items-center justify-between mb-4">
                <div
                  className="w-12 h-12 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: `${stat.color}15`, color: stat.color }}
                >
                  {stat.icon}
                </div>
                <div className="text-right">
                  <div className={`text-sm ${stat.trend > 0 ? 'text-green-500' : 'text-red-500'}`}>
                    <ArrowUpRight className={`w-4 h-4 inline ${stat.trend < 0 ? 'rotate-90' : ''}`} />
                    {Math.abs(stat.trend)}%
                  </div>
                </div>
              </div>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.prefix}
                suffix={stat.suffix}
                valueStyle={{ color: stat.color, fontSize: '24px', fontWeight: 'bold' }}
              />
              <Text type="secondary" className="text-xs">{stat.description}</Text>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表和数据展示 */}
      <Row gutter={[16, 16]} className="mb-6">
        {/* 费用趋势图 */}
        <Col xs={24} lg={16}>
          <Card title="费用趋势" extra={<Button type="link">查看详情</Button>}>
            <ReactECharts option={costTrendOption} style={{ height: '300px' }} />
          </Card>
        </Col>

        {/* 实例状态 */}
        <Col xs={24} lg={8}>
          <Card title="实例状态" extra={<Button type="link">管理实例</Button>}>
            <ReactECharts option={instanceStatusOption} style={{ height: '300px' }} />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24}>
          <Card title="快速操作">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div
                onClick={() => navigate('/compute-market')}
                className="group cursor-pointer p-6 rounded-lg border border-gray-200 hover:border-blue-400 hover:shadow-lg transition-all duration-300 text-center"
              >
                <div className="w-12 h-12 mx-auto mb-4 bg-blue-50 rounded-lg flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                  <ShoppingCart className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-1">租用实例</h3>
                <p className="text-sm text-gray-500">浏览算力市场</p>
              </div>

              <div
                onClick={() => navigate('/device-hosting')}
                className="group cursor-pointer p-6 rounded-lg border border-gray-200 hover:border-green-400 hover:shadow-lg transition-all duration-300 text-center"
              >
                <div className="w-12 h-12 mx-auto mb-4 bg-green-50 rounded-lg flex items-center justify-center group-hover:bg-green-100 transition-colors">
                  <HardDrive className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-1">托管设备</h3>
                <p className="text-sm text-gray-500">接入您的设备</p>
              </div>

              <div
                onClick={() => navigate('/app-market')}
                className="group cursor-pointer p-6 rounded-lg border border-gray-200 hover:border-purple-400 hover:shadow-lg transition-all duration-300 text-center"
              >
                <div className="w-12 h-12 mx-auto mb-4 bg-purple-50 rounded-lg flex items-center justify-center group-hover:bg-purple-100 transition-colors">
                  <Store className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-1">应用市场</h3>
                <p className="text-sm text-gray-500">一键部署应用</p>
              </div>

              <div
                onClick={() => navigate('/profile')}
                className="group cursor-pointer p-6 rounded-lg border border-gray-200 hover:border-orange-400 hover:shadow-lg transition-all duration-300 text-center"
              >
                <div className="w-12 h-12 mx-auto mb-4 bg-orange-50 rounded-lg flex items-center justify-center group-hover:bg-orange-100 transition-colors">
                  <User className="w-6 h-6 text-orange-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-1">查看账单</h3>
                <p className="text-sm text-gray-500">费用明细</p>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最近活动 */}
      <Row gutter={[16, 16]}>
        <Col xs={24}>
          <Card title="最近活动" extra={<Button type="link">查看全部</Button>}>
            <Timeline
              items={[
                {
                  color: 'green',
                  children: (
                    <div>
                      <div className="font-medium">DeepSeek训练实例已启动</div>
                      <Text type="secondary" className="text-xs">2小时前</Text>
                    </div>
                  ),
                },
                {
                  color: 'blue',
                  children: (
                    <div>
                      <div className="font-medium">安装了Jupyter Lab应用</div>
                      <Text type="secondary" className="text-xs">4小时前</Text>
                    </div>
                  ),
                },
                {
                  color: 'orange',
                  children: (
                    <div>
                      <div className="font-medium">图像渲染实例已停止</div>
                      <Text type="secondary" className="text-xs">1天前</Text>
                    </div>
                  ),
                },
                {
                  color: 'gray',
                  children: (
                    <div>
                      <div className="font-medium">创建了新的存储卷</div>
                      <Text type="secondary" className="text-xs">2天前</Text>
                    </div>
                  ),
                },
              ]}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
